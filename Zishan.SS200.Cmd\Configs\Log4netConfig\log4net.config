<?xml version="1.0" encoding="utf-8" ?>
<log4net>
	<!-- 缓冲错误日志 - 高优先级，无损失 -->
	<appender name="bufferedErrorAppender" type="log4net.Appender.BufferingForwardingAppender">
		<bufferSize value="256" />
		<lossy value="false" />
		<fix value="Partial" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="ERROR" />
		</evaluator>
		<appender-ref ref="errorFileAppender" />
	</appender>
	<!-- 错误文件输出 - 优化性能 -->
	<appender name="errorFileAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="ERROR" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\err.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<maxSizeRollBackups value="30" />
		<maximumFileSize value="50MB" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
	</appender>

	<!-- 缓冲警告日志 -->
	<appender name="bufferedWarnAppender" type="log4net.Appender.BufferingForwardingAppender">
		<bufferSize value="128" />
		<lossy value="false" />
		<fix value="Partial" />
		<appender-ref ref="warnFileAppender" />
	</appender>
	<!-- 警告文件输出 - 优化性能 -->
	<appender name="warnFileAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="WARN" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\warn.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<maxSizeRollBackups value="15" />
		<maximumFileSize value="30MB" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
	</appender>

	<!-- 缓冲信息日志 - 允许丢失以提高性能 -->
	<appender name="bufferedInfoAppender" type="log4net.Appender.BufferingForwardingAppender">
		<bufferSize value="512" />
		<lossy value="true" />
		<fix value="Partial" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="WARN" />
		</evaluator>
		<appender-ref ref="infoFileAppender" />
	</appender>
	<!-- 信息文件输出 - 优化性能 -->
	<appender name="infoFileAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="INFO" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\info.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="100MB" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
	</appender>

	<!-- 调试日志（仅开发环境） - 直接输出，不缓冲 -->
	<appender name="debugFileAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="DEBUG" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\debug.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<maxSizeRollBackups value="3" />
		<maximumFileSize value="50MB" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
	</appender>

	<!-- 缓冲性能日志 - 专用于性能监控 -->
	<appender name="bufferedPerfAppender" type="log4net.Appender.BufferingForwardingAppender">
		<bufferSize value="100" />
		<lossy value="false" />
		<fix value="Partial" />
		<appender-ref ref="perfFileAppender" />
	</appender>
	<!-- 性能文件输出 -->
	<appender name="perfFileAppender" type="log4net.Appender.RollingFileAppender">
		<file value="Logs\perf.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<maxSizeRollBackups value="7" />
		<maximumFileSize value="20MB" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.fff} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
	</appender>

	<!-- 根日志配置 - 使用缓冲appender提高性能 -->
	<root>
		<!--日志等级 OFF > FATAL > ERROR > WARN > INFO > DEBUG > ALL-->
		<level value="INFO" />
		<appender-ref ref="bufferedErrorAppender" />
		<appender-ref ref="bufferedWarnAppender" />
		<appender-ref ref="bufferedInfoAppender" />
		<!-- 调试环境启用 -->
		<!--<appender-ref ref="debugFileAppender" />-->
	</root>

	<!-- 性能日志记录器 -->
	<logger name="Performance" additivity="false">
		<level value="INFO" />
		<appender-ref ref="bufferedPerfAppender" />
	</logger>

	<!-- 特定组件日志级别控制 -->
	<logger name="Zishan.SS200.Cmd.Services" additivity="true">
		<level value="INFO" />
	</logger>

	<logger name="Zishan.SS200.Cmd.Models" additivity="true">
		<level value="WARN" />
	</logger>

	<!-- UI相关日志 -->
	<logger name="Zishan.SS200.Cmd.ViewModels" additivity="true">
		<level value="INFO" />
	</logger>

	<!-- Modbus通信日志 -->
	<logger name="NModbus" additivity="true">
		<level value="WARN" />
	</logger>
</log4net>